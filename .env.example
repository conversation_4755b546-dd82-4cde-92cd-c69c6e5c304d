# Convex Configuration
# Run npx convex dev or bunx convex dev and it'll get CONVEX_DEPLOYMENT and NEXT_PUBLIC_CONVEX_URL 
# These are also available from your Convex dashboard at https://dashboard.convex.dev


# Clerk Authentication & Billing
# Get these from your Clerk dashboard at https://dashboard.clerk.com
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key_here
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key_here
# Note: CLERK_WEBHOOK_SECRET should be set in your Convex dashboard environment variables
# Do not add it to this .env.local file


# Clerk Frontend API URL
# This comes from your JWT template in Clerk dashboard (create a template named "convex")
NEXT_PUBLIC_CLERK_FRONTEND_API_URL=https://your-clerk-frontend-api-url.clerk.accounts.dev


# Clerk Redirect URLs
# These ensure users are redirected to dashboard after authentication
NEXT_PUBLIC_CLERK_SIGN_IN_FORCE_REDIRECT_URL=/dashboard
NEXT_PUBLIC_CLERK_SIGN_UP_FORCE_REDIRECT_URL=/dashboard
NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL=/dashboard
NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL=/dashboard


